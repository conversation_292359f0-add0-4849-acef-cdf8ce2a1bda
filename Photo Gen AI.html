<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>Human Character Generator — Hugging Face Inference API</title>
  <style>
    :root{--gap:12px;--pad:12px;--radius:14px;--border:#e1e5ea;--text:#0f172a;--muted:#475569;--bg:#f8fafc}
    *{box-sizing:border-box}
    body{font-family:system-ui, Segoe UI, Roboto, Arial, sans-serif;margin:0;background:var(--bg);color:var(--text)}
    header{position:sticky;top:0;z-index:5;background:white;border-bottom:1px solid var(--border)}
    header .wrap{max-width:1100px;margin:auto;padding:14px var(--pad);display:flex;gap:10px;align-items:center;justify-content:space-between}
    main{max-width:1100px;margin:auto;padding:18px var(--pad);display:grid;grid-template-columns:380px 1fr;gap:var(--gap)}
    @media (max-width: 980px){main{grid-template-columns:1fr}}
    h1{font-size:20px;margin:0}
    .tip{color:var(--muted);font-size:13px}
    .card{background:white;border:1px solid var(--border);border-radius:var(--radius);padding:var(--pad)}
    .grid{display:grid;grid-template-columns:repeat(2,minmax(0,1fr));gap:var(--gap)}
    .grid-3{display:grid;grid-template-columns:repeat(3,minmax(0,1fr));gap:var(--gap)}
    .field{display:flex;flex-direction:column;gap:6px}
    label{font-weight:600;font-size:13px}
    select, input[type="text"], input[type="number"], textarea{width:100%;padding:9px 10px;border:1px solid var(--border);border-radius:10px;background:white}
    select[multiple]{height:180px}
    textarea{min-height:80px;resize:vertical}
    .row{display:flex;gap:8px;align-items:center;flex-wrap:wrap}
    button{appearance:none;border:1px solid var(--border);background:#111827;color:#fff;padding:10px 14px;border-radius:12px;font-weight:700;cursor:pointer}
    button.secondary{background:white;color:#111827}
    button:disabled{opacity:.6;cursor:progress}
    .imgwrap{display:grid;place-items:center;min-height:360px;border:1px dashed var(--border);border-radius:var(--radius);background:#fff}
    img{max-width:100%;height:auto;border-radius:12px}
    .row > .spacer{flex:1}
    .small{font-size:12px;color:var(--muted)}
    .danger{color:#b42318}
    .ok{color:#047857}
    .mono{font-family:ui-monospace, SFMono-Regular, Menlo, Consolas, monospace;font-size:12px}
    details{border:1px solid var(--border);border-radius:10px;padding:10px;background:#fff}
    summary{cursor:pointer;font-weight:600}
  </style>
</head>
<body>
  <header>
    <div class="wrap">
      <h1>Human Character Generator</h1>
      <div class="tip">Hugging Face Inference API • client-side demo</div>
    </div>
  </header>

  <main>
    <!-- LEFT: Controls -->
    <section class="card" id="controls">
      <div class="field">
        <label>Hugging Face Access Token <span class="small">(paste your <a href="https://huggingface.co/settings/tokens" target="_blank" rel="noreferrer">hf_…</a> token)</span></label>
        <input id="token" type="text" placeholder="hf_********************************" value="*************************************" />
        <div class="small danger">Do <b>not</b> deploy this page publicly with your token. For production, put the token on a backend and call it from there.</div>
      </div>

      <div class="field">
        <label>Model</label>
        <select id="model">
          <option value="black-forest-labs/FLUX.1-dev">FLUX.1-dev (photoreal / general)</option>
          <option value="stabilityai/sdxl-turbo">SDXL Turbo (fast)</option>
          <option value="stabilityai/stable-diffusion-3.5-large">Stable Diffusion 3.5 Large</option>
          <option value="stabilityai/stable-diffusion-3-medium-diffusers">SD 3 Medium (Diffusers)</option>
          <option value="Lykon/dreamshaper-8">DreamShaper 8</option>
          <option value="SG161222/RealVisXL_V4.0_Lightning">RealVisXL Lightning</option>
        </select>
      </div>

      <hr style="margin:14px 0;border:none;border-top:1px solid var(--border)">

      <div class="grid">
        <div class="field">
          <label>Style</label>
          <select id="style">
            <option value="">(auto)</option>
            <option>photorealistic</option>
            <option>cinematic</option>
            <option>documentary</option>
            <option>studio portrait</option>
            <option>anime</option>
            <option>manga</option>
            <option>cartoon</option>
            <option>comic book</option>
            <option>3D render</option>
            <option>oil painting</option>
            <option>watercolor</option>
            <option>ink illustration</option>
            <option>pixel art</option>
            <option>pastel drawing</option>
            <option>charcoal sketch</option>
          </select>
        </div>
        <div class="field">
          <label>Ethnicity / appearance</label>
          <select id="ethnicity">
            <option value="">(auto)</option>
            <option>African</option>
            <option>East Asian</option>
            <option>South Asian</option>
            <option>Southeast Asian</option>
            <option>Middle Eastern / North African</option>
            <option>European</option>
            <option>Latino / Hispanic</option>
            <option>Indigenous</option>
            <option>Mixed</option>
            <option>Unspecified</option>
          </select>
        </div>
        <div class="field">
          <label>Gender</label>
          <select id="gender">
            <option value="">(auto)</option>
            <option>female</option>
            <option>male</option>
            <option>non-binary</option>
            <option>androgynous</option>
          </select>
        </div>
        <div class="field">
          <label>Age</label>
          <select id="age">
            <option value="">(auto)</option>
            <option>young adult</option>
            <option>adult</option>
            <option>middle-aged</option>
            <option>senior</option>
          </select>
          <div class="small">(This tool is designed for <b>adults</b> only.)</div>
        </div>
        <div class="field">
          <label>Pose</label>
          <select id="pose">
            <option value="">(auto)</option>
            <option>standing</option>
            <option>walking</option>
            <option>running</option>
            <option>sitting</option>
            <option>reclining</option>
            <option>profile left</option>
            <option>profile right</option>
            <option>3/4 view</option>
            <option>arms crossed</option>
            <option>hands on hips</option>
            <option>action jump</option>
            <option>victory pose</option>
            <option>T-pose</option>
            <option>A-pose</option>
          </select>
        </div>
        <div class="field">
          <label>Framing</label>
          <select id="framing">
            <option value="">(auto)</option>
            <option>portrait close-up</option>
            <option>half body</option>
            <option>full body</option>
            <option>wide shot</option>
          </select>
        </div>
      </div>

      <div class="grid">
        <div class="field">
          <label>Background</label>
          <select id="background">
            <option value="">(auto)</option>
            <option>studio backdrop</option>
            <option>plain white background</option>
            <option>urban street</option>
            <option>nature landscape</option>
            <option>interior room</option>
            <option>fantasy scene</option>
            <option>scifi environment</option>
          </select>
        </div>
        <div class="field">
          <label>Lighting</label>
          <select id="lighting">
            <option value="">(auto)</option>
            <option>soft lighting</option>
            <option>hard lighting</option>
            <option>rim lighting</option>
            <option>golden hour</option>
            <option>studio three-point lighting</option>
            <option>dramatic shadows</option>
          </select>
        </div>
      </div>

      <div class="grid">
        <div class="field">
          <label>Hair</label>
          <select id="hair">
            <option value="">(auto)</option>
            <option>short hair</option>
            <option>long hair</option>
            <option>curly hair</option>
            <option>straight hair</option>
            <option>braids</option>
            <option>ponytail</option>
            <option>shaved</option>
            <option>dreadlocks</option>
          </select>
        </div>
        <div class="field">
          <label>Eyes</label>
          <select id="eyes">
            <option value="">(auto)</option>
            <option>brown eyes</option>
            <option>blue eyes</option>
            <option>green eyes</option>
            <option>hazel eyes</option>
            <option>grey eyes</option>
          </select>
        </div>
        <div class="field">
          <label>Facial hair (optional)</label>
          <select id="facial_hair">
            <option value="">(none/auto)</option>
            <option>clean-shaven</option>
            <option>stubble</option>
            <option>mustache</option>
            <option>beard</option>
          </select>
        </div>
      </div>

      <div class="field">
        <label>Body parts focus (multi-select)</label>
        <select id="body_parts" multiple>
          <option>full body</option>
          <option>head</option>
          <option>face</option>
          <option>hair</option>
          <option>eyebrows</option>
          <option>eyes</option>
          <option>nose</option>
          <option>mouth</option>
          <option>teeth</option>
          <option>ears</option>
          <option>neck</option>
          <option>shoulders</option>
          <option>chest</option>
          <option>back</option>
          <option>abdomen</option>
          <option>waist</option>
          <option>hips</option>
          <option>buttocks</option>
          <option>groin</option>
          <option>upper arms</option>
          <option>forearms</option>
          <option>elbows</option>
          <option>wrists</option>
          <option>hands</option>
          <option>fingers</option>
          <option>thighs</option>
          <option>knees</option>
          <option>calves</option>
          <option>ankles</option>
          <option>feet</option>
          <option>toes</option>
          <option>skin</option>
        </select>
        <div class="small">Hold <b>Ctrl/⌘</b> to select multiple.</div>
      </div>

      <div class="grid">
        <div class="field">
          <label>Top</label>
          <select id="top">
            <option value="">(auto)</option>
            <option>t-shirt</option>
            <option>shirt</option>
            <option>blouse</option>
            <option>hoodie</option>
            <option>sweater</option>
            <option>jacket</option>
            <option>coat</option>
            <option>dress</option>
            <option>tunic</option>
            <option>armor breastplate</option>
            <option>kimono</option>
            <option>abaya</option>
            <option>sari</option>
            <option>hanbok</option>
		    <option>Naked</option> <!-- added as you asked -->
			<option>Casual Dress</option>
			<option>Evening Gown</option>
			<option>Cocktail Dress</option>
			<option>Summer Dress</option>
			<option>Maxi Dress</option>
			<option>Mini Dress</option>
			<option>Ball Gown</option>
			<option>Bridal Dress</option>
			<option>Sundress</option>
			<option>Slip Dress</option>
			<!-- Tops -->
			<option>T-Shirt</option>
			<option>Blouse</option>
			<option>Cropped Top</option>
			<option>Sweater</option>
			<option>Tunic</option>
			<option>Tank Top</option>
			<option>Cardigan</option>
			<!-- Bottoms -->
			<option>Jeans</option>
			<option>Leggings</option>
			<option>Skirt (Mini)</option>
			<option>Skirt (Midi)</option>
			<option>Skirt (Maxi)</option>
			<option>Shorts</option>
			<option>Trousers</option>
			<option>Capri Pants</option>
			<!-- Outerwear -->
			<option>Jacket</option>
			<option>Blazer</option>
			<option>Coat</option>
			<option>Trench Coat</option>
			<option>Hoodie</option>
			<option>Leather Jacket</option>
			<option>Denim Jacket</option>
			<!-- Swimwear & Activewear -->
			<option>Bikini</option>
			<option>One-Piece Swimsuit</option>
			<option>Sportswear</option>
			<option>Yoga Outfit</option>
			<option>Gym Outfit</option>
			<!-- Traditional -->
			<option>Kimono</option>
			<option>Sari</option>
			<option>Abaya</option>
			<option>Hanbok</option>
			<option>Cheongsam (Qipao)</option>
			<option>Dirndl</option>
			<option>Kaftan</option>
			<option>African Dress</option>
			<!-- Professional & Uniforms -->
			<option>Business Suit</option>
			<option>Nurse Uniform</option>
			<option>School Uniform</option>
			<option>Maid Outfit</option>
			<option>Military Uniform</option>
			<!-- Fantasy & Costumes -->
			<option>Armor</option>
			<option>Fantasy Gown</option>
			<option>Witch Outfit</option>
			<option>Elf Costume</option>
			<option>Vampire Outfit</option>
			<option>Angel Costume</option>
			<option>Demon Costume</option>
          </select>
        </div>
        <div class="field">
          <label>Bottom</label>
          <select id="bottom">
            <option value="">(auto)</option>
            <option>jeans</option>
            <option>trousers</option>
            <option>shorts</option>
            <option>skirt</option>
            <option>dress (full)</option>
            <option>leggings</option>
            <option>armor greaves</option>
            <option>hakama</option>
			<option>Naked</option> 
          </select>
        </div>
        <div class="field">
          <label>Footwear</label>
          <select id="shoes">
            <option value="">(auto)</option>
            <option>boots</option>
            <option>sneakers</option>
            <option>sandals</option>
            <option>heels</option>
            <option>barefoot</option>
          </select>
        </div>
        <div class="field">
          <label>Accessories (optional)</label>
          <select id="accessories">
            <option value="">(none)</option>
            <option>glasses</option>
            <option>hat</option>
            <option>earrings</option>
            <option>necklace</option>
            <option>scarf</option>
            <option>gloves</option>
            <option>belt</option>
            <option>watch</option>
            <option>bag</option>
          </select>
        </div>
      </div>

      <div class="grid">
        <div class="field">
          <label>Negative prompt (optional)</label>
          <textarea id="negative" placeholder="blurry, low quality, extra fingers, watermark, logo, nsfw"></textarea>
        </div>
      </div>

      <div class="grid">
        <div class="field">
          <label>Width</label>
          <input id="width" type="number" min="256" max="1536" step="64" value="768" />
        </div>
        <div class="field">
          <label>Height</label>
          <input id="height" type="number" min="256" max="1536" step="64" value="1024" />
        </div>
        <div class="field">
          <label>Steps</label>
          <input id="steps" type="number" min="4" max="80" step="1" value="28" />
        </div>
        <div class="field">
          <label>Guidance scale</label>
          <input id="guidance" type="number" min="0" max="20" step="0.5" value="7.5" />
        </div>
        <div class="field">
          <label>Seed (optional)</label>
          <input id="seed" type="number" min="0" step="1" placeholder="random" />
        </div>
      </div>

      <div class="row" style="margin-top:10px">
        <button id="randomize" type="button" class="secondary">Randomize fields</button>
        <div class="spacer"></div>
        <button id="generate" type="button">Generate Image</button>
      </div>

      <div class="field" style="margin-top:10px">
        <label>Final prompt (auto-generated)</label>
        <textarea id="prompt" class="mono" readonly></textarea>
      </div>

      <details style="margin-top:10px">
        <summary>Dev notes</summary>
        <div class="small">
          This page calls the Hugging Face Inference API directly from the browser using <code>fetch</code> and your token. For production, create a tiny proxy (Cloudflare Worker / Node / Python) that adds the <code>Authorization</code> header server‑side and apply rate‑limits.
        </div>
      </details>
    </section>

    <!-- RIGHT: Output -->
    <section class="card">
      <div class="imgwrap" id="preview">
        <div class="small">Generated image will appear here</div>
      </div>
      <div class="row" style="margin-top:10px">
        <a id="download" class="secondary" style="display:none" download="hf-image.png">
          <button class="secondary" type="button">Download image</button>
        </a>
        <div class="spacer"></div>
        <div id="status" class="small"></div>
      </div>
    </section>
  </main>

  <script>
    // --- Utilities
    const $ = (id) => document.getElementById(id);
    const pick = (arr) => arr[Math.floor(Math.random() * arr.length)];

    const lists = {
      style: ["photorealistic","cinematic","documentary","studio portrait","anime","manga","cartoon","comic book","3D render","oil painting","watercolor","ink illustration","pixel art","pastel drawing","charcoal sketch"],
      ethnicity: ["African","East Asian","South Asian","Southeast Asian","Middle Eastern / North African","European","Latino / Hispanic","Indigenous","Mixed","Unspecified"],
      gender: ["female","male","non-binary","androgynous"],
      age: ["young adult","adult","middle-aged","senior"],
      pose: ["standing","walking","running","sitting","reclining","profile left","profile right","3/4 view","arms crossed","hands on hips","action jump","victory pose","T-pose","A-pose"],
      framing: ["portrait close-up","half body","full body","wide shot"],
      background: ["studio backdrop","plain white background","urban street","nature landscape","interior room","fantasy scene","scifi environment"],
      lighting: ["soft lighting","hard lighting","rim lighting","golden hour","studio three-point lighting","dramatic shadows"],
      hair: ["short hair","long hair","curly hair","straight hair","braids","ponytail","shaved","dreadlocks"],
      eyes: ["brown eyes","blue eyes","green eyes","hazel eyes","grey eyes"],
      facial_hair: ["clean-shaven","stubble","mustache","beard"],
      top: ["t-shirt","shirt","blouse","hoodie","sweater","jacket","coat","dress","tunic","armor breastplate","kimono","abaya","sari","hanbok"],
      bottom: ["jeans","trousers","shorts","skirt","dress (full)","leggings","armor greaves","hakama"],
      shoes: ["boots","sneakers","sandals","heels","barefoot"],
      accessories: ["","glasses","hat","earrings","necklace","scarf","gloves","belt","watch","bag"],
      body_parts: ["full body","head","face","hair","eyebrows","eyes","nose","mouth","teeth","ears","neck","shoulders","chest","back","abdomen","waist","hips","buttocks","groin","upper arms","forearms","elbows","wrists","hands","fingers","thighs","knees","calves","ankles","feet","toes","skin"],
    };

    function selectedMulti(id){
      const sel = $(id);
      return Array.from(sel.selectedOptions).map(o => o.value);
    }

    function valOrRandom(id, key){
      const v = $(id).value.trim();
      return v || pick(lists[key]);
    }

    function buildPrompt(){
      const style = $("style").value;
      const ethnicity = $("ethnicity").value;
      const gender = $("gender").value;
      const age = $("age").value;
      const pose = $("pose").value;
      const framing = $("framing").value;
      const background = $("background").value;
      const lighting = $("lighting").value;
      const hair = $("hair").value;
      const eyes = $("eyes").value;
      const facial_hair = $("facial_hair").value;
      const body_parts = selectedMulti("body_parts");
      const top = $("top").value;
      const bottom = $("bottom").value;
      const shoes = $("shoes").value;
      const accessories = $("accessories").value;

      const bits = [];
      bits.push(`a ${age||'adult'} ${gender||'person'} with ${ethnicity||'unspecified'} appearance`);
      bits.push(pose||'standing');
      bits.push(framing||'full body');
      if (style) bits.push(style);
      if (hair) bits.push(hair);
      if (eyes) bits.push(eyes);
      if (facial_hair) bits.push(facial_hair);
      bits.push(`wearing ${top||'outfit'}${bottom?(' and '+bottom):''}${shoes?(' with '+shoes):''}`);
      if (accessories) bits.push(`accessories: ${accessories}`);
      if (body_parts.length) bits.push(`focus on: ${body_parts.join(', ')}`);
      if (background) bits.push(`background: ${background}`);
      if (lighting) bits.push(`lighting: ${lighting}`);

      return bits.join(", ");
    }

    function randomizeAll(){
      for (const key of Object.keys(lists)){
        const el = $(key);
        if (!el) continue;
        if (el.tagName === 'SELECT' && !el.multiple){
          const opts = Array.from(el.options).map(o=>o.value);
          const choices = opts.filter(Boolean);
          el.value = pick(choices);
        }
        if (el && el.multiple){
          // pick 2-4 at random
          const values = lists[key];
          const n = Math.max(1, Math.min(4, Math.floor(Math.random()*4)+1));
          const picks = new Set();
          while (picks.size < n){ picks.add(pick(values)); }
          Array.from(el.options).forEach(o => o.selected = picks.has(o.value));
        }
      }
      $("negative").value = "blurry, low quality, extra fingers, watermark, logo, text, nsfw";
      updatePromptText();
    }

    function updatePromptText(){
      $("prompt").value = buildPrompt();
    }

    for (const id of ["style","ethnicity","gender","age","pose","framing","background","lighting","hair","eyes","facial_hair","top","bottom","shoes","accessories","body_parts","negative","width","height","steps","guidance","seed"]) {
      const el = $(id);
      if (!el) continue;
      el.addEventListener(el.multiple? 'change' : 'input', updatePromptText);
    }

    $("randomize").addEventListener("click", randomizeAll);

    // Initial prompt
    updatePromptText();

    async function callHF(prompt, settings){
      const token = $("token").value.trim();
      if (!token) throw new Error("Please paste your Hugging Face token.");
      const model = $("model").value;

      const url = `https://api-inference.huggingface.co/models/${encodeURIComponent(model)}`;

      const payload = {
        inputs: prompt,
        parameters: {
          negative_prompt: settings.negative,
          num_inference_steps: settings.steps,
          guidance_scale: settings.guidance,
          width: settings.width,
          height: settings.height,
          seed: settings.seed || undefined
        }
      };

      const res = await fetch(url, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
          'Accept': 'image/png'
        },
        body: JSON.stringify(payload)
      });

      if (!res.ok){
        const txt = await res.text();
        throw new Error(`HTTP ${res.status}: ${txt}`);
      }

      const blob = await res.blob();
      return blob;
    }

    function setStatus(msg, ok=false){
      const el = $("status");
      el.textContent = msg;
      el.className = "small " + (ok? 'ok' : 'danger');
    }

    $("generate").addEventListener("click", async () => {
      try{
        $("generate").disabled = true;
        setStatus("Generating… this can take a few seconds");
        const prompt = buildPrompt();
        $("prompt").value = prompt;

        const settings = {
          negative: $("negative").value.trim() || undefined,
          width: parseInt($("width").value,10) || 768,
          height: parseInt($("height").value,10) || 1024,
          steps: parseInt($("steps").value,10) || 28,
          guidance: parseFloat($("guidance").value) || 7.5,
          seed: $("seed").value ? parseInt($("seed").value,10) : undefined
        };

        const blob = await callHF(prompt, settings);
        const url = URL.createObjectURL(blob);
        const imgWrap = $("preview");
        imgWrap.innerHTML = '';
        const img = new Image();
        img.src = url;
        img.alt = "Generated image";
        img.onload = () => URL.revokeObjectURL(url);
        imgWrap.appendChild(img);

        const dl = $("download");
        dl.style.display = 'inline-block';
        dl.href = url;
        setStatus("Done", true);
      } catch(err){
        console.error(err);
        setStatus(err.message || String(err));
      } finally {
        $("generate").disabled = false;
      }
    });
  </script>
</body>
</html>